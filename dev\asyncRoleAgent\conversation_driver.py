import asyncio
import random
import time
from typing import Dict, Any, Callable, Optional
from datetime import datetime

class ConversationDriver:
    def __init__(self):
        self.is_active = False
        self.conversation_task = None
        self.message_callback = None
        self.context_provider = None
        self.last_proactive_time = 0
        self.proactive_intervals = [3, 5, 8, 12, 15]  # 随机间隔秒数
        self.max_proactive_messages = 3  # 最大连续主动消息数
        self.proactive_count = 0
        self.last_user_interaction = time.time()
        
    def set_message_callback(self, callback: Callable[[str, str], None]):
        """设置消息回调函数 callback(role, message)"""
        self.message_callback = callback
    
    def set_context_provider(self, provider: Callable[[], Dict[str, Any]]):
        """设置上下文提供者函数"""
        self.context_provider = provider
    
    async def start_conversation_loop(self):
        """启动对话循环"""
        if self.is_active:
            return
            
        self.is_active = True
        self.conversation_task = asyncio.create_task(self._conversation_loop())
    
    async def stop_conversation_loop(self):
        """停止对话循环"""
        self.is_active = False
        if self.conversation_task:
            self.conversation_task.cancel()
            try:
                await self.conversation_task
            except asyncio.CancelledError:
                pass
    
    def on_user_interaction(self):
        """用户交互时调用，重置主动对话计数"""
        self.last_user_interaction = time.time()
        self.proactive_count = 0
    
    def should_send_proactive_message(self) -> bool:
        """判断是否应该发送主动消息"""
        current_time = time.time()
        
        # 检查是否超过最大连续主动消息数
        if self.proactive_count >= self.max_proactive_messages:
            return False
        
        # 检查距离上次用户交互的时间
        time_since_user_interaction = current_time - self.last_user_interaction
        if time_since_user_interaction < 2:  # 用户刚交互过，不要立即主动
            return False
        
        # 检查距离上次主动消息的时间
        time_since_last_proactive = current_time - self.last_proactive_time
        min_interval = random.choice(self.proactive_intervals)
        
        return time_since_last_proactive >= min_interval
    
    async def _conversation_loop(self):
        """主对话循环"""
        while self.is_active:
            try:
                # 检查是否有完成的任务需要通知
                await self._check_completed_tasks()
                
                # 检查是否需要发送主动消息
                if self.should_send_proactive_message():
                    await self._send_proactive_message()
                
                # 短暂休眠，避免过度占用CPU
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                print(f"对话循环错误: {e}")
                await asyncio.sleep(5)  # 错误后等待更长时间
    
    async def _check_completed_tasks(self):
        """检查并处理完成的任务"""
        if not self.context_provider:
            return
        
        context = self.context_provider()
        task_manager = context.get('task_manager')
        
        if not task_manager:
            return
        
        # 获取刚完成的任务
        completed_task_id = await task_manager.get_next_completed_task()
        if completed_task_id:
            task_info = task_manager.get_task_status(completed_task_id)
            if task_info and task_info['status'].value == 'completed':
                await self._handle_task_completion(task_info)
    
    async def _handle_task_completion(self, task_info: Dict[str, Any]):
        """处理任务完成"""
        if not self.message_callback or not self.context_provider:
            return
        
        context = self.context_provider()
        prompt_system = context.get('prompt_system')
        
        if not prompt_system:
            return
        
        # 获取任务类型和结果
        task_type = task_info['type'].value if hasattr(task_info['type'], 'value') else str(task_info['type'])
        task_result = task_info.get('result', {})
        
        # 生成任务完成消息
        completion_message = prompt_system.get_task_completion_message(task_type, task_result)
        
        # 发送消息
        await self._send_message("assistant", completion_message)
        
        # 重置主动对话计数（因为刚发了消息）
        self.proactive_count = 0
        self.last_proactive_time = time.time()
    
    async def _send_proactive_message(self):
        """发送主动消息"""
        if not self.message_callback or not self.context_provider:
            return
        
        context = self.context_provider()
        llm_client = context.get('llm_client')
        prompt_system = context.get('prompt_system')
        
        if not llm_client or not prompt_system:
            return
        
        try:
            # 构建主动对话prompt
            proactive_prompt = prompt_system.build_proactive_prompt(context)
            
            # 获取LLM响应
            messages = [{"role": "system", "content": proactive_prompt}]
            response = await self._call_llm_async(llm_client, messages)
            
            if response:
                await self._send_message("assistant", response)
                self.proactive_count += 1
                self.last_proactive_time = time.time()
        
        except Exception as e:
            print(f"发送主动消息失败: {e}")
    
    async def _call_llm_async(self, llm_client, messages):
        """异步调用LLM"""
        try:
            # 这里需要根据实际的LLM客户端实现异步调用
            # 如果LLM客户端不支持异步，可以使用线程池
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, llm_client.chat, messages)
            
            if hasattr(response, 'choices') and response.choices:
                return response.choices[0].message.content
            return None
            
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return None
    
    async def _send_message(self, role: str, content: str):
        """发送消息"""
        if self.message_callback:
            # 在主线程中调用回调
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.message_callback, role, content)
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        current_time = time.time()
        return {
            "is_active": self.is_active,
            "proactive_count": self.proactive_count,
            "max_proactive_messages": self.max_proactive_messages,
            "time_since_last_proactive": current_time - self.last_proactive_time,
            "time_since_user_interaction": current_time - self.last_user_interaction,
            "next_proactive_interval": random.choice(self.proactive_intervals)
        }

class ConversationState:
    """对话状态管理"""
    def __init__(self):
        self.conversation_history = []
        self.active_topics = []
        self.user_preferences = {}
        self.session_start_time = time.time()
        
    def add_message(self, role: str, content: str, metadata: Optional[Dict] = None):
        """添加消息到历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": time.time(),
            "metadata": metadata or {}
        }
        self.conversation_history.append(message)
        
        # 保持历史长度在合理范围内
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-40:]
    
    def get_recent_messages(self, count: int = 10) -> list:
        """获取最近的消息"""
        return self.conversation_history[-count:]
    
    def get_conversation_duration(self) -> float:
        """获取对话持续时间（秒）"""
        return time.time() - self.session_start_time
    
    def add_topic(self, topic: str):
        """添加活跃话题"""
        if topic not in self.active_topics:
            self.active_topics.append(topic)
        
        # 保持话题列表长度
        if len(self.active_topics) > 5:
            self.active_topics = self.active_topics[-3:]
    
    def get_context_summary(self) -> Dict[str, Any]:
        """获取上下文摘要"""
        recent_messages = self.get_recent_messages(5)
        
        return {
            "message_count": len(self.conversation_history),
            "recent_messages": recent_messages,
            "active_topics": self.active_topics,
            "conversation_duration": self.get_conversation_duration(),
            "user_preferences": self.user_preferences
        }

class MessageQueue:
    """消息队列，用于管理待发送的消息"""
    def __init__(self):
        self.queue = asyncio.Queue()
        self.processing = False
        
    async def add_message(self, role: str, content: str, priority: int = 0):
        """添加消息到队列"""
        message = {
            "role": role,
            "content": content,
            "priority": priority,
            "timestamp": time.time()
        }
        await self.queue.put(message)
    
    async def process_messages(self, callback: Callable):
        """处理消息队列"""
        if self.processing:
            return
            
        self.processing = True
        try:
            while not self.queue.empty():
                message = await self.queue.get()
                await callback(message["role"], message["content"])
                await asyncio.sleep(0.5)  # 消息间隔
        finally:
            self.processing = False
