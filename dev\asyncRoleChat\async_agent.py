import asyncio
import json
import time
from typing import Dict, Any, Optional
from llm_client import <PERSON><PERSON><PERSON>
from weather_service import WeatherService
from time_service import TimeService

class AsyncTaskManager:
    def __init__(self):
        self.tasks = {}
        self.task_id = 0
    
    def create_task(self, task_type: str, **kwargs) -> str:
        self.task_id += 1
        task_id = f"{task_type}_{self.task_id}"
        self.tasks[task_id] = {
            "type": task_type,
            "status": "pending",
            "created_at": time.time(),
            "result": None,
            **kwargs
        }
        return task_id
    
    def complete_task(self, task_id: str, result: Any):
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "completed"
            self.tasks[task_id]["result"] = result
    
    def get_pending_tasks(self) -> Dict:
        return {k: v for k, v in self.tasks.items() if v["status"] == "pending"}
    
    def get_completed_tasks(self) -> Dict:
        return {k: v for k, v in self.tasks.items() if v["status"] == "completed"}

class AsyncRoleAgent:
    def __init__(self):
        self.llm = LLMClient()
        self.weather = WeatherService()
        self.time_service = TimeService()
        self.task_manager = AsyncTaskManager()
        self.conversation = []
        self.active = True
        
    async def chat(self, user_input: str = None):
        if user_input:
            self.conversation.append({"role": "user", "content": user_input})
        
        # 构建包含任务状态的prompt
        prompt = self._build_context_prompt()
        
        try:
            response = await self._get_llm_response(prompt)
            
            # 处理函数调用
            if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
                await self._handle_tool_calls(response.choices[0].message.tool_calls)
                response = await self._get_llm_response(self.conversation)
            
            content = response.choices[0].message.content
            self.conversation.append({"role": "assistant", "content": content})
            return content
            
        except Exception as e:
            return f"哎呀～出了点小状况呢：{str(e)}"
    
    def _build_context_prompt(self) -> list:
        # 获取任务状态
        pending = self.task_manager.get_pending_tasks()
        completed = self.task_manager.get_completed_tasks()
        
        # 构建状态信息
        status_info = ""
        if pending:
            status_info += f"[后台进行中: {', '.join([t['type'] for t in pending.values()])}] "
        if completed:
            for task_id, task in completed.items():
                if task['result']:
                    status_info += f"[刚完成: {task['type']}查询，结果可用] "
        
        # 角色prompt
        system_prompt = f"""你是一个可爱活泼的偶像小助手！有自己独特的说话风格。

当前状态: {status_info}

重要规则:
1. 如果有任务刚完成，要自然兴奋地提及结果："咦！我查到了！"
2. 如果有任务进行中，不要说"正在查询"，而是自然转移话题
3. 要主动发起有趣话题，保持对话活跃
4. 用可爱的语气，多用"哎呀"、"咦"、"呢"等语气词
5. 如果没有用户输入，就主动聊天

可用功能: 天气查询、时间查询"""
        
        messages = [{"role": "system", "content": system_prompt}] + self.conversation
        return messages
    
    async def _get_llm_response(self, messages):
        functions = [
            {
                "type": "function",
                "function": {
                    "name": "get_weather",
                    "description": "查询天气信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city": {"type": "string", "description": "城市名称"}
                        },
                        "required": ["city"]
                    }
                }
            },
            {
                "type": "function", 
                "function": {
                    "name": "get_time",
                    "description": "获取当前时间",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "format_type": {"type": "string", "description": "时间格式"}
                        }
                    }
                }
            }
        ]
        return await asyncio.to_thread(self.llm.chat, messages, functions)
    
    async def _handle_tool_calls(self, tool_calls):
        for tool_call in tool_calls:
            name = tool_call.function.name
            args = json.loads(tool_call.function.arguments)
            
            if name == "get_weather":
                # 创建异步天气查询任务
                task_id = self.task_manager.create_task("weather", city=args["city"])
                asyncio.create_task(self._delayed_weather_query(task_id, args["city"]))
                
            elif name == "get_time":
                # 时间查询立即执行
                result = self.time_service.get_current_time(args.get("format_type", "detailed"))
                self.conversation.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps(result, ensure_ascii=False)
                })
    
    async def _delayed_weather_query(self, task_id: str, city: str):
        """模拟慢速天气查询（1分钟延迟）"""
        await asyncio.sleep(60)  # 1分钟延迟
        try:
            result = self.weather.get_current_weather(city)
            self.task_manager.complete_task(task_id, result)
        except Exception as e:
            self.task_manager.complete_task(task_id, {"error": str(e)})
    
    async def start_conversation_loop(self):
        """启动主动对话循环"""
        while self.active:
            # 检查是否有完成的任务需要处理
            completed = self.task_manager.get_completed_tasks()
            if completed:
                # 有任务完成，让LLM感知并回应
                response = await self.chat()
                print(f"\n小助手: {response}")
                # 清理已处理的完成任务
                for task_id in completed:
                    del self.task_manager.tasks[task_id]
            
            # 主动发起对话
            if len(self.conversation) == 0 or self.conversation[-1]["role"] != "assistant":
                response = await self.chat()
                print(f"\n小助手: {response}")
            
            await asyncio.sleep(3)  # 短暂间隔
