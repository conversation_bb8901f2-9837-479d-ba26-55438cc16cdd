import asyncio
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

from async_task_manager import AsyncTaskManager, TaskType
from role_prompt_system import RolePromptSystem
from conversation_driver import ConversationDriver, ConversationState
from llm_client import LLMClient
from service_integrator import ServiceIntegrator, ALL_FUNCTIONS
from config import Config

class AsyncRoleAgent:
    def __init__(self):
        # 核心组件
        self.task_manager = AsyncTaskManager()
        self.prompt_system = RolePromptSystem()
        self.conversation_driver = ConversationDriver()
        self.conversation_state = ConversationState()
        self.llm_client = LLMClient()
        self.service_integrator = ServiceIntegrator()
        
        # 状态管理
        self.is_active = False
        self.pending_user_response = False
        self.last_completed_tasks = []
        
        # 设置回调
        self.conversation_driver.set_message_callback(self._on_proactive_message)
        self.conversation_driver.set_context_provider(self._get_context)
        
        # 消息回调（由外部设置）
        self.message_callback = None
    
    def set_message_callback(self, callback):
        """设置消息回调函数 callback(role, message)"""
        self.message_callback = callback
    
    async def start(self):
        """启动异步代理"""
        if self.is_active:
            return
            
        self.is_active = True
        
        # 启动对话驱动器
        if Config.PROACTIVE_CHAT_ENABLED:
            await self.conversation_driver.start_conversation_loop()
        
        # 发送启动消息
        welcome_message = "嗨！我是小雨，你的可爱天气时间偶像小助手呢！✨ 有什么想聊的吗？"
        await self._send_message("assistant", welcome_message)
    
    async def stop(self):
        """停止异步代理"""
        self.is_active = False
        await self.conversation_driver.stop_conversation_loop()
    
    async def chat(self, user_input: str) -> str:
        """处理用户输入"""
        if not self.is_active:
            await self.start()
        
        # 记录用户交互
        self.conversation_driver.on_user_interaction()
        self.conversation_state.add_message("user", user_input)
        
        try:
            # 分析用户意图并可能启动任务
            await self._analyze_and_execute_tasks(user_input)
            
            # 生成回应
            response = await self._generate_response(user_input)
            
            # 记录助手回应
            self.conversation_state.add_message("assistant", response)
            
            return response
            
        except Exception as e:
            error_response = f"哎呀，出现了一点小问题呢：{str(e)} 不过没关系，我们继续聊天吧！"
            self.conversation_state.add_message("assistant", error_response)
            return error_response
    
    async def _analyze_and_execute_tasks(self, user_input: str):
        """分析用户输入并执行相应任务"""
        user_input_lower = user_input.lower()
        
        # 提取城市名称
        city = self._extract_city_name(user_input)
        
        # 检查天气查询意图
        weather_keywords = ['天气', '温度', '下雨', '晴天', '阴天', '多云', '风']
        if any(keyword in user_input for keyword in weather_keywords):
            if city:
                await self.task_manager.start_weather_query(city, Config.WEATHER_QUERY_DELAY)
            else:
                await self.task_manager.start_weather_query("北京", Config.WEATHER_QUERY_DELAY)  # 默认城市
        
        # 检查预报查询意图
        forecast_keywords = ['预报', '明天', '后天', '未来', '几天']
        if any(keyword in user_input for keyword in forecast_keywords):
            days = self._extract_days(user_input)
            if city:
                await self.task_manager.start_forecast_query(city, days, Config.FORECAST_QUERY_DELAY)
            else:
                await self.task_manager.start_forecast_query("北京", days, Config.FORECAST_QUERY_DELAY)
        
        # 检查时间查询意图
        time_keywords = ['时间', '几点', '现在', '日期', '星期']
        if any(keyword in user_input for keyword in time_keywords):
            format_type = "simple" if "几点" in user_input else "detailed"
            await self.task_manager.start_time_query(format_type)
    
    def _extract_city_name(self, text: str) -> Optional[str]:
        """从文本中提取城市名称"""
        # 常见城市列表
        cities = [
            "北京", "上海", "广州", "深圳", "杭州", "南京", "苏州", "成都", "重庆", "武汉",
            "西安", "天津", "青岛", "大连", "厦门", "宁波", "无锡", "长沙", "郑州", "济南",
            "福州", "合肥", "昆明", "南昌", "贵阳", "太原", "石家庄", "哈尔滨", "长春", "沈阳"
        ]
        
        for city in cities:
            if city in text:
                return city
        
        # 使用正则表达式匹配可能的城市名
        city_pattern = r'([一-龥]{2,4}[市县区]?)'
        matches = re.findall(city_pattern, text)
        if matches:
            return matches[0]
        
        return None
    
    def _extract_days(self, text: str) -> int:
        """从文本中提取天数"""
        # 查找数字
        import re
        numbers = re.findall(r'\d+', text)
        if numbers:
            days = int(numbers[0])
            return min(max(days, 1), 5)  # 限制在1-5天
        
        # 默认值
        if "明天" in text:
            return 1
        elif "后天" in text:
            return 2
        else:
            return 3
    
    async def _generate_response(self, user_input: str) -> str:
        """生成对用户输入的回应"""
        # 获取当前状态
        pending_tasks = self.task_manager.get_pending_tasks()
        completed_tasks = self._get_new_completed_tasks()
        
        # 构建系统prompt
        conversation_history = self.conversation_state.get_recent_messages(10)
        system_prompt = self.prompt_system.build_system_prompt(
            pending_tasks, completed_tasks, conversation_history
        )
        
        # 构建消息
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加对话历史
        for msg in conversation_history[-5:]:  # 最近5条消息
            messages.append({"role": msg["role"], "content": msg["content"]})
        
        # 添加当前用户输入
        messages.append({"role": "user", "content": user_input})
        
        # 调用LLM
        response = await self.llm_client.chat_async(messages, functions=ALL_FUNCTIONS)
        
        # 处理函数调用
        if hasattr(response.choices[0].message, 'function_call') and response.choices[0].message.function_call:
            function_result = await self._handle_function_call(response.choices[0].message.function_call)
            
            # 添加函数调用结果到对话
            messages.append({
                "role": "function",
                "name": response.choices[0].message.function_call.name,
                "content": json.dumps(function_result, ensure_ascii=False)
            })
            
            # 再次调用LLM生成最终回应
            final_response = await self.llm_client.chat_async(messages)
            return final_response.choices[0].message.content
        
        return response.choices[0].message.content
    
    async def _handle_function_call(self, function_call) -> Dict[str, Any]:
        """处理函数调用"""
        name = function_call.name
        args = json.loads(function_call.arguments)
        
        try:
            if name == "get_current_weather":
                return await self.service_integrator.execute_weather_query(args["city"])
            elif name == "get_weather_forecast":
                days = args.get("days", 3)
                return await self.service_integrator.execute_forecast_query(args["city"], days)
            elif name == "get_current_time":
                format_type = args.get("format_type", "detailed")
                return await self.service_integrator.execute_time_query(format_type)
            elif name == "get_time_info":
                info_type = args.get("info_type", "period")
                return await self.service_integrator.execute_time_info_query(info_type)
            else:
                raise Exception(f"未知函数: {name}")
                
        except Exception as e:
            return {"error": str(e), "description": f"执行{name}时出错：{str(e)}"}
    
    def _get_new_completed_tasks(self) -> List[str]:
        """获取新完成的任务"""
        completed_tasks = list(self.task_manager.get_completed_tasks().keys())
        new_completed = [task_id for task_id in completed_tasks if task_id not in self.last_completed_tasks]
        self.last_completed_tasks = completed_tasks
        return new_completed
    
    def _get_context(self) -> Dict[str, Any]:
        """获取上下文信息（供ConversationDriver使用）"""
        return {
            "task_manager": self.task_manager,
            "prompt_system": self.prompt_system,
            "llm_client": self.llm_client,
            "conversation_state": self.conversation_state,
            "pending_tasks": self.task_manager.get_pending_tasks(),
            "completed_tasks": self.task_manager.get_completed_tasks()
        }
    
    async def _on_proactive_message(self, role: str, content: str):
        """处理主动消息"""
        self.conversation_state.add_message(role, content, {"proactive": True})
        await self._send_message(role, content)
    
    async def _send_message(self, role: str, content: str):
        """发送消息"""
        if self.message_callback:
            # 在主线程中调用回调
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self.message_callback, role, content)
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态"""
        return {
            "is_active": self.is_active,
            "task_summary": self.task_manager.get_task_summary(),
            "conversation_stats": self.conversation_driver.get_conversation_stats(),
            "conversation_summary": self.conversation_state.get_context_summary()
        }
    
    async def reset(self):
        """重置代理状态"""
        await self.stop()
        self.conversation_state = ConversationState()
        self.task_manager.clear_completed_tasks()
        self.last_completed_tasks = []
