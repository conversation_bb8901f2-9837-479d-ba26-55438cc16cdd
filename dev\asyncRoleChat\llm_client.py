import openai
from config import Config

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL
        )
    
    def chat(self, messages, tools=None):
        params = {
            "model": Config.MODEL_NAME,
            "messages": messages,
            "temperature": 0.8,
            "max_tokens": 1000
        }
        
        if tools:
            params["tools"] = tools
            params["tool_choice"] = "auto"
        
        return self.client.chat.completions.create(**params)
